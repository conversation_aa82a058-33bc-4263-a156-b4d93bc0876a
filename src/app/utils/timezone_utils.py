"""Timezone utilities for the application."""

from datetime import datetime, timedelta, timezone, date, time
from typing import Op<PERSON>, Tuple
from pytz import timezone as pytz_timezone
import pytz



# Define IST timezone
IST = pytz_timezone('Asia/Kolkata')


def get_ist_now() -> datetime:
    """
    Get current IST time as a timezone-aware datetime.
    """
    return datetime.now(IST)


def convert_to_ist(dt: datetime) -> datetime:
    """
    Convert any datetime to timezone-aware IST datetime.
    Returns aware datetime (with +05:30 offset).
    """
    if dt is None:
        return None
    try:
        if dt.tzinfo is None:
            # Naive datetime: localize it to IST
            return IST.localize(dt)
        # Aware datetime: convert to IST
        return dt.astimezone(IST)
    except Exception:
        try:
            return IST.localize(dt)
        except Exception:
            return dt


def format_ist_datetime(dt: datetime) -> Optional[str]:
    """
    Format datetime to ISO format string.
    Since database stores times in IST, no conversion needed.
    Example: '2025-07-26T17:02:53.566039'
    """
    if dt is None:
        return None
    try:
        # Database already stores times in IST, so no conversion needed
        # Just format the datetime as is
        return dt.replace(tzinfo=None).isoformat(timespec='microseconds')
    except Exception:
        return dt.replace(tzinfo=None).isoformat(timespec='microseconds') if dt else None

def get_utc_range_from_ist_dates(start_date: date, end_date: Optional[date] = None) -> tuple[datetime, datetime]:
    """
    Convert start and end dates in IST to a UTC datetime range.
    """
    if end_date is None:
        end_date = start_date

    start_dt_ist = IST.localize(datetime.combine(start_date, time.min))
    end_dt_ist = IST.localize(datetime.combine(end_date, time.max))
    return (
        start_dt_ist.astimezone(timezone.utc),
        end_dt_ist.astimezone(timezone.utc)
    )
    
    
def get_ist_range(start_date: date, end_date: Optional[date] = None) -> Tuple[datetime, datetime]:
    """
    Returns inclusive datetime range in IST timezone.
    Use when DB stores timestamps in IST.
    """
    if end_date is None:
        end_date = start_date

    start_dt = IST.localize(datetime.combine(start_date, time.min))
    end_dt = IST.localize(datetime.combine(end_date, time.max))
    return start_dt, end_dt

